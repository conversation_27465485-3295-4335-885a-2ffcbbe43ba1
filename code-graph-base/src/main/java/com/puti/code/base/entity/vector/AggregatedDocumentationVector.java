package com.puti.code.base.entity.vector;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聚合说明书向量实体类
 * 用于在Milvus中存储聚合说明书的向量表示
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregatedDocumentationVector {
    
    /**
     * 聚合说明书ID（对应MySQL中的ID）
     */
    private String aggregatedDocumentationId;
    
    /**
     * 聚合标题
     */
    private String title;
    
    /**
     * 聚合摘要
     */
    private String summary;
    
    /**
     * 聚合类型
     */
    private String aggregationType;
    
    /**
     * 项目ID
     */
    private String projectId;
    
    /**
     * 分支名称
     */
    private String branchName;
    
    /**
     * 稠密向量
     */
    private float[] textDense;
    
    /**
     * 创建时间（毫秒时间戳）
     */
    private Long createdAt;
    
    /**
     * 更新时间（毫秒时间戳）
     */
    private Long updatedAt;
}
