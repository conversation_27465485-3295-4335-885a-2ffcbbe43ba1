package com.puti.code.documentation.repository.sql.mapper;

import com.puti.code.base.entity.rdb.DocumentationAggregation;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 说明书聚合关系数据访问Mapper
 *
 * <AUTHOR>
 */
@Mapper
@Repository
public interface DocumentationAggregationMapper {

    /**
     * 插入聚合关系
     */
    @Insert("""
                INSERT INTO documentation_aggregation (
                    aggregated_documentation_id, documentation_id, entry_point_id,
                    entry_point_name, weight, created_at
                ) VALUES (
                    #{aggregatedDocumentationId}, #{documentationId}, #{entryPointId},
                    #{entryPointName}, #{weight}, #{createdAt}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DocumentationAggregation aggregation);

    /**
     * 批量插入聚合关系
     */
    @Insert({
            "<script>",
            "INSERT INTO documentation_aggregation (",
            "aggregated_documentation_id, documentation_id, entry_point_id,",
            "entry_point_name, weight, created_at",
            ") VALUES ",
            "<foreach collection='list' item='item' separator=','>",
            "(#{item.aggregatedDocumentationId}, #{item.documentationId}, #{item.entryPointId},",
            "#{item.entryPointName}, #{item.weight}, #{item.createdAt})",
            "</foreach>",
            "</script>"
    })
    int batchInsert(List<DocumentationAggregation> aggregations);

    /**
     * 根据ID查询聚合关系
     */
    @Select("""
                SELECT id, aggregated_documentation_id, documentation_id, entry_point_id,
                       entry_point_name, weight, created_at
                FROM documentation_aggregation
                WHERE id = #{id}
            """)
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "aggregatedDocumentationId", column = "aggregated_documentation_id"),
            @Result(property = "documentationId", column = "documentation_id"),
            @Result(property = "entryPointId", column = "entry_point_id"),
            @Result(property = "entryPointName", column = "entry_point_name"),
            @Result(property = "weight", column = "weight"),
            @Result(property = "createdAt", column = "created_at")
    })
    DocumentationAggregation findById(Long id);

    /**
     * 根据聚合说明书ID查询所有关系
     */
    @Select("""
                SELECT id, aggregated_documentation_id, documentation_id, entry_point_id,
                       entry_point_name, weight, created_at
                FROM documentation_aggregation
                WHERE aggregated_documentation_id = #{aggregatedDocumentationId}
                ORDER BY weight DESC, created_at ASC
            """)
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "aggregatedDocumentationId", column = "aggregated_documentation_id"),
            @Result(property = "documentationId", column = "documentation_id"),
            @Result(property = "entryPointId", column = "entry_point_id"),
            @Result(property = "entryPointName", column = "entry_point_name"),
            @Result(property = "weight", column = "weight"),
            @Result(property = "createdAt", column = "created_at")
    })
    List<DocumentationAggregation> findByAggregatedDocumentationId(Long aggregatedDocumentationId);

    /**
     * 根据原始说明书ID查询聚合关系
     */
    @Select("""
                SELECT id, aggregated_documentation_id, documentation_id, entry_point_id,
                       entry_point_name, weight, created_at
                FROM documentation_aggregation
                WHERE documentation_id = #{documentationId}
                ORDER BY created_at DESC
            """)
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "aggregatedDocumentationId", column = "aggregated_documentation_id"),
            @Result(property = "documentationId", column = "documentation_id"),
            @Result(property = "entryPointId", column = "entry_point_id"),
            @Result(property = "entryPointName", column = "entry_point_name"),
            @Result(property = "weight", column = "weight"),
            @Result(property = "createdAt", column = "created_at")
    })
    List<DocumentationAggregation> findByDocumentationId(Long documentationId);

    /**
     * 根据入口点ID查询聚合关系
     */
    @Select("""
                SELECT id, aggregated_documentation_id, documentation_id, entry_point_id,
                       entry_point_name, weight, created_at
                FROM documentation_aggregation
                WHERE entry_point_id = #{entryPointId}
                ORDER BY created_at DESC
            """)
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "aggregatedDocumentationId", column = "aggregated_documentation_id"),
            @Result(property = "documentationId", column = "documentation_id"),
            @Result(property = "entryPointId", column = "entry_point_id"),
            @Result(property = "entryPointName", column = "entry_point_name"),
            @Result(property = "weight", column = "weight"),
            @Result(property = "createdAt", column = "created_at")
    })
    List<DocumentationAggregation> findByEntryPointId(String entryPointId);

    /**
     * 删除聚合关系
     */
    @Delete("DELETE FROM documentation_aggregation WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 根据聚合说明书ID删除所有关系
     */
    @Delete("DELETE FROM documentation_aggregation WHERE aggregated_documentation_id = #{aggregatedDocumentationId}")
    int deleteByAggregatedDocumentationId(Long aggregatedDocumentationId);

    /**
     * 统计聚合关系总数
     */
    @Select("SELECT COUNT(*) FROM documentation_aggregation")
    long count();
}
