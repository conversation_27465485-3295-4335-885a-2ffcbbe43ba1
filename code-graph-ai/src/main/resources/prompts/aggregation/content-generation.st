# 聚合说明书生成

## 任务描述
请基于以下相关的流程说明书，生成一个综合的聚合说明书。

## 聚合信息
- **聚合类型**: {aggregationType}
- **聚合描述**: {description}

## 相关说明书
{docsContent}

## 生成要求

### 内容整合原则
1. **完整性**: 整合所有相关流程的核心信息
2. **连贯性**: 确保内容逻辑清晰，结构完整
3. **去重性**: 避免重复内容，突出互补信息
4. **关联性**: 突出各个流程之间的关联性和整体业务价值

### 标题要求
- 简洁明了，体现聚合的核心功能
- 使用中文，避免技术术语
- 一句话概括整个聚合的业务价值

### 摘要要求
- 200字以内的简洁摘要
- 包含业务简述和关键词
- 避免技术术语，专注业务语义
- 用于生成向量检索，应包含相关业务关键词

### 内容要求
- 使用Markdown格式
- 结构清晰，层次分明
- 包含以下主要部分：
  - 业务概述
  - 核心流程说明
  - 各流程间的关联关系
  - 关键业务规则
  - 异常处理机制
  - 业务价值和意义

### 整合策略
- **横向整合**: 将同类型的功能进行合并和去重
- **纵向整合**: 建立不同流程间的逻辑关系和依赖链
- **深度分析**: 基于多个流程信息进行更深入的业务洞察
- **价值提升**: 生成比单个流程更有价值的综合性文档

## 输出格式
请严格按照以下JSON格式返回结果，不要添加任何其他内容：

```json
{
  "title": "聚合说明书标题",
  "summary": "聚合说明书摘要",
  "content": "聚合说明书详细内容（Markdown格式）"
}
```

## 特别注意
- 只返回JSON对象，不要包含其他解释文字
- content字段必须是完整的Markdown格式内容
- 确保生成的文档比单个流程说明书更有价值
- 突出整体业务流程的完整性和一致性
