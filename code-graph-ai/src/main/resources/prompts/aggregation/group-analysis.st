# 流程说明书聚合分析

## 任务描述
请分析以下流程说明书列表，根据功能相关性将它们分组聚合。

## 说明书列表
{summariesJson}

## 分析要求

### 分组原则
1. **功能相关性**: 根据业务功能的相关性进行分组
2. **合理规模**: 每个分组应包含2-8个相关的说明书
3. **业务完整性**: 确保每个分组代表一个完整的业务流程或功能模块
4. **避免重复**: 每个说明书只能属于一个分组

### 聚合类型命名
- 使用简洁明了的中文名称
- 体现核心业务功能（如：订单流程、用户管理、支付流程等）
- 避免技术术语，专注业务语义

### 相关性评分标准
- **90-100分**: 高度相关，属于同一核心业务流程
- **70-89分**: 中度相关，属于相关业务模块
- **50-69分**: 低度相关，有一定业务关联
- **50分以下**: 不建议分组

### 分组描述要求
- 简洁描述该分组包含的业务功能
- 突出各个说明书之间的关联性
- 说明为什么这些说明书应该聚合在一起

## 输出格式
请严格按照以下JSON格式返回结果，不要添加任何其他内容：

```json
[
  {
    "aggregationType": "订单流程",
    "description": "包含订单创建、修改、取消等相关流程",
    "documentationSummaries": [
      {
        "entryPointId": "...",
        "entryPointName": "...",
        "title": "...",
        "summary": "...",
        "documentationId": ...
      }
    ],
    "relevanceScore": 85
  }
]
```

## 特别注意
- 只返回JSON数组，不要包含其他解释文字
- 确保所有输入的说明书都被分配到某个分组中
- 相关性评分必须是0-100之间的整数
- 如果某个说明书与其他说明书相关性都很低，可以单独成组
