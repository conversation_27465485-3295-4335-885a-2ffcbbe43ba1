# 第{level}层{levelDescription}分析

## 分析任务
请分析以下Java代码的第{level}层调用关系，这是从入口点 `{entryPoint}` 的{levelDescription}。

## 项目信息
- **入口点**: {entryPoint}
- **层级**: 第{level}层
- **方法数量**: {methodCount}个
- **项目**: {projectId}
- **分支**: {branchName}

{if(previousDocumentation)}
## 上一层分析总结
{previousDocumentation}
{endif}

## 代码内容
{codeContent}

## 分析要求
{analysisRequirements}

## 输出结构
请严格按照以下结构进行分析和输出：

{documentStructure}
